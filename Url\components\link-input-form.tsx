"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { LinkIcon, PlayIcon } from "lucide-react"

interface LinkInputFormProps {
  onProcessStart: () => void
  onProcessComplete: (sessionId: string) => void
}

export function LinkInputForm({ onProcessStart, onProcessComplete }: LinkInputFormProps) {
  const [audioUrl, setAudioUrl] = useState("")
  const [error, setError] = useState("")
  const [isLoading, setIsLoading] = useState(false)

  const validateUrl = (url: string) => {
    try {
      new URL(url)
      return true
    } catch {
      return false
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError("")

    if (!audioUrl.trim()) {
      setError("Please enter an audio URL")
      return
    }

    if (!validateUrl(audioUrl)) {
      setError("Please enter a valid URL")
      return
    }

    setIsLoading(true)
    onProcessStart()

    try {
      const response = await fetch("/api/process", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ audioUrl }),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || "Failed to process audio")
      }

      // Simulate processing time
      setTimeout(() => {
        onProcessComplete(data.sessionId)
        setIsLoading(false)
      }, 3000)
    } catch (err) {
      setError(err instanceof Error ? err.message : "An error occurred")
      setIsLoading(false)
    }
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <LinkIcon className="h-5 w-5" />
          Audio URL Input
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Input
              type="url"
              placeholder="https://example.com/audio-file.mp3"
              value={audioUrl}
              onChange={(e) => setAudioUrl(e.target.value)}
              className="w-full"
              disabled={isLoading}
            />
            <p className="text-sm text-muted-foreground">Paste the URL of your audio file (MP3, WAV, M4A supported)</p>
          </div>

          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <Button type="submit" className="w-full" disabled={isLoading} size="lg">
            <PlayIcon className="h-4 w-4 mr-2" />
            {isLoading ? "Processing..." : "Generate Transcript & Summary"}
          </Button>
        </form>
      </CardContent>
    </Card>
  )
}
