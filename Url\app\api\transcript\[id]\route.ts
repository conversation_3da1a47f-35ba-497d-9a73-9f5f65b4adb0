import { type NextRequest, NextResponse } from "next/server"

// Mock transcript data
const mockTranscript = [
  {
    speaker: "<PERSON>",
    timestamp: "00:00:15",
    text: "Good morning everyone, thank you for joining today's quarterly review meeting. I'd like to start by discussing our progress on the key initiatives we outlined last quarter.",
  },
  {
    speaker: "<PERSON>",
    timestamp: "00:00:45",
    text: "Thanks <PERSON>. I'm happy to report that our customer acquisition numbers have exceeded expectations. We've seen a 25% increase in new signups compared to last quarter.",
  },
  {
    speaker: "<PERSON>",
    timestamp: "00:01:20",
    text: "That's fantastic news Sarah. From the product development side, we've successfully launched three major features that our users have been requesting. The feedback has been overwhelmingly positive.",
  },
  {
    speaker: "<PERSON>",
    timestamp: "00:02:00",
    text: "Excellent work team. Let's dive into the specific metrics and discuss our action items for the next quarter. <PERSON>, can you walk us through the detailed analytics?",
  },
  {
    speaker: "<PERSON>",
    timestamp: "00:02:30",
    text: "Absolutely. Our conversion rate has improved by 15%, and customer retention is at an all-time high of 94%. We should focus on expanding our marketing efforts in the European market.",
  },
  {
    speaker: "<PERSON>",
    timestamp: "00:03:15",
    text: "I agree with <PERSON>'s recommendation. The European market presents a significant opportunity. We should also consider localizing our product features for different regions.",
  },
  {
    speaker: "<PERSON>",
    timestamp: "00:03:45",
    text: "Great points. Let's make European expansion a priority for next quarter. Mike, can you prepare a technical feasibility report for localization?",
  },
  {
    speaker: "<PERSON>",
    timestamp: "00:04:10",
    text: "Absolutely, I'll have that ready by next week. We should also consider the infrastructure requirements for supporting multiple regions.",
  },
]

export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const sessionId = params.id

    if (!sessionId) {
      return NextResponse.json({ error: "Session ID is required" }, { status: 400 })
    }

    // In a real implementation, this would fetch from MongoDB
    console.log(`[v0] Fetching transcript for session: ${sessionId}`)

    // Simulate some processing delay
    await new Promise((resolve) => setTimeout(resolve, 500))

    return NextResponse.json({
      transcript: mockTranscript,
      sessionId,
    })
  } catch (error) {
    console.error("[v0] Error fetching transcript:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
