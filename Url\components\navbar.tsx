import { But<PERSON> } from "@/components/ui/button"
import { MicIcon, HomeIcon } from "lucide-react"

export function Navbar() {
  return (
    <nav className="border-b bg-card">
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <MicIcon className="h-8 w-8 text-primary" />
            <span className="text-xl font-bold text-foreground">AI Meeting Transcriber</span>
          </div>
          <Button variant="ghost" size="sm">
            <HomeIcon className="h-4 w-4 mr-2" />
            Home
          </Button>
        </div>
      </div>
    </nav>
  )
}
