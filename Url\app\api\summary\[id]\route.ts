import { type NextRequest, NextResponse } from "next/server"

// Mock summary data
const mockSummary = {
  keySummary:
    "The quarterly review meeting highlighted significant achievements across customer acquisition and product development. The team exceeded growth targets with a 25% increase in new signups and successfully launched three major product features. Customer metrics show strong performance with 94% retention rate and 15% improvement in conversion rates. The discussion concluded with plans for European market expansion as a key priority for the next quarter.",
  actionItems: [
    "Expand marketing efforts in the European market",
    "Prepare technical feasibility report for product localization",
    "Analyze user feedback from the three newly launched features",
    "Prepare detailed quarterly report for stakeholders",
    "Schedule follow-up meeting to discuss European expansion strategy",
    "Review infrastructure requirements for multi-region support",
    "Optimize conversion funnel based on improved metrics",
  ],
  topics: [
    "Quarterly Review",
    "Customer Acquisition",
    "Product Development",
    "Marketing Strategy",
    "European Expansion",
    "User Feedback",
    "Performance Metrics",
    "Localization",
    "Infrastructure Planning",
  ],
}

export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const sessionId = params.id

    if (!sessionId) {
      return NextResponse.json({ error: "Session ID is required" }, { status: 400 })
    }

    // In a real implementation, this would fetch from MongoDB
    console.log(`[v0] Fetching summary for session: ${sessionId}`)

    // Simulate some processing delay
    await new Promise((resolve) => setTimeout(resolve, 800))

    return NextResponse.json({
      summary: mockSummary,
      sessionId,
    })
  } catch (error) {
    console.error("[v0] Error fetching summary:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
