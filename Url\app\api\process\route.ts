import { type NextRequest, NextResponse } from "next/server";

const GEMINI_API_KEY = "AIzaSyBxc-xChG1Q3C460hiDrZv4xbDcIG6lH98";

export async function POST(request: NextRequest) {
  let response: any = {};
  let status: number = 200;
  try {
    const { url } = await request.json();
    if (!url) {
      response = { error: "URL is required" };
      status = 400;
    } else {
      // Validate URL format
      let validUrl = true;
      try {
        new URL(url);
      } catch {
        validUrl = false;
      }
      if (!validUrl) {
        response = { error: "Invalid URL format" };
        status = 400;
      } else {
        // Fetch the content from the URL
        const pageRes = await fetch(url);
        const pageContent = await pageRes.text();
        // Call Gemini (Google AI) for summarization
        try {
          const geminiRes = await fetch(
            `https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=${GEMINI_API_KEY}`,
            {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
              },
              body: JSON.stringify({
                contents: [
                  {
                    parts: [
                      {
                        text: `Summarize and transcribe the following content:\n${pageContent}`,
                      },
                    ],
                  },
                ],
              }),
            }
          );
          const geminiData = await geminiRes.json();
          const summary =
            geminiData?.candidates?.[0]?.content?.parts?.[0]?.text || "No summary available.";
          response = {
            transcript: summary,
            summary,
          };
        } catch (error: any) {
          console.error("[Gemini] Error processing URL:", error);
          response = {
            transcript: "Error processing URL.",
            summary: "Error processing URL.",
            error: error.message || "Unknown error.",
          };
          status = 500;
        }
      }
    }
  } catch (error: any) {
    console.error("[Gemini] Error processing URL:", error);
    response = {
      transcript: "Error processing URL.",
      summary: "Error processing URL.",
      error: error.message || "Unknown error.",
    };
    status = 500;
  }
  return NextResponse.json(response, { status });
}
