"use client"

import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { DownloadIcon, FileTextIcon, FileIcon, BookOpenIcon } from "lucide-react"
import { useToast } from "@/hooks/use-toast"

interface ExportOptionsProps {
  sessionId: string
}

export function ExportOptions({ sessionId }: ExportOptionsProps) {
  const { toast } = useToast()

  const handleExport = async (format: "txt" | "pdf" | "docx") => {
    try {
      const response = await fetch(`/api/export/${sessionId}/${format}`)

      if (!response.ok) {
        throw new Error("Export failed")
      }

      // In a real implementation, this would trigger a file download
      toast({
        title: "Export Started",
        description: `Your ${format.toUpperCase()} file is being prepared for download.`,
      })
    } catch (error) {
      toast({
        title: "Export Failed",
        description: "There was an error exporting your file. Please try again.",
        variant: "destructive",
      })
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <DownloadIcon className="h-5 w-5" />
          Export Options
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex flex-col sm:flex-row gap-3">
          <Button onClick={() => handleExport("txt")} variant="outline" className="flex-1">
            <FileTextIcon className="h-4 w-4 mr-2" />
            Export as TXT
          </Button>
          <Button onClick={() => handleExport("pdf")} variant="outline" className="flex-1">
            <FileIcon className="h-4 w-4 mr-2" />
            Export as PDF
          </Button>
          <Button onClick={() => handleExport("docx")} variant="outline" className="flex-1">
            <BookOpenIcon className="h-4 w-4 mr-2" />
            Export as DOCX
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
