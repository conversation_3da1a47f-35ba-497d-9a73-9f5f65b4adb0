"use client"

import { useState } from "react"
import { Navbar } from "@/components/navbar"
import { LinkInputForm } from "@/components/link-input-form"
import { TranscriptDisplay } from "@/components/transcript-display"
import { SummaryPanel } from "@/components/summary-panel"
import { ExportOptions } from "@/components/export-options"

export default function Home() {
  const [sessionId, setSessionId] = useState<string | null>(null)
  const [isProcessing, setIsProcessing] = useState(false)

  const handleProcessComplete = (id: string) => {
    setSessionId(id)
    setIsProcessing(false)
  }

  const handleProcessStart = () => {
    setIsProcessing(true)
    setSessionId(null)
  }

  return (
    <div className="min-h-screen bg-background">
      <Navbar />

      <main className="container mx-auto px-4 py-8">
        {!sessionId && !isProcessing && (
          <div className="max-w-2xl mx-auto">
            <div className="text-center mb-8">
              <h1 className="text-4xl font-bold text-foreground mb-4">AI Meeting Transcriber</h1>
              <p className="text-lg text-muted-foreground">
                Transform your audio recordings into detailed transcripts and summaries using AI
              </p>
            </div>
            <LinkInputForm onProcessStart={handleProcessStart} onProcessComplete={handleProcessComplete} />
          </div>
        )}

        {isProcessing && (
          <div className="max-w-2xl mx-auto text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <h2 className="text-2xl font-semibold mb-2">Processing Audio...</h2>
            <p className="text-muted-foreground">
              Our AI is analyzing your audio and generating the transcript. This may take a few minutes.
            </p>
          </div>
        )}

        {sessionId && !isProcessing && (
          <div className="space-y-6">
            <div className="flex flex-col lg:flex-row gap-6">
              <div className="lg:w-2/3">
                <TranscriptDisplay sessionId={sessionId} />
              </div>
              <div className="lg:w-1/3">
                <SummaryPanel sessionId={sessionId} />
              </div>
            </div>
            <ExportOptions sessionId={sessionId} />
          </div>
        )}
      </main>
    </div>
  )
}
